{"indexes": [{"collectionGroup": "roadmapMetadata", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "lastAccessed", "order": "DESCENDING"}]}, {"collectionGroup": "roadmapMetadata", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isPublic", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "phaseTasks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "roadmapId", "order": "ASCENDING"}, {"fieldPath": "phaseNumber", "order": "ASCENDING"}]}], "fieldOverrides": []}