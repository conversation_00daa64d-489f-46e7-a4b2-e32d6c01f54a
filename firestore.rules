rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    // ============================================================================
    // HELPER FUNCTIONS
    // ============================================================================

    // Check if user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }

    // Check if user owns the resource
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }

    // Check if the resource is public
    function isPublic() {
      return resource.data.isPublic == true;
    }

    // Validate user data structure
    function isValidUserData() {
      return request.resource.data.keys().hasAll(['displayName', 'email', 'createdAt']) &&
             request.resource.data.displayName is string &&
             request.resource.data.email is string &&
             request.resource.data.email == request.auth.token.email;
    }

    // Validate roadmap data structure (updated for split format)
    function isValidRoadmapData() {
      return request.resource.data.keys().hasAll(['userId', 'outline', 'isPublic', 'createdAt']) &&
             request.resource.data.userId is string &&
             request.resource.data.userId == request.auth.uid &&
             request.resource.data.isPublic is bool &&
             request.resource.data.outline is map &&
             (!request.resource.data.keys().hasAny(['allowDownload']) || request.resource.data.allowDownload is bool);
    }

    // Validate phase tasks data structure
    function isValidPhaseTasksData() {
      return request.resource.data.keys().hasAll(['roadmapId', 'phaseId', 'tasks', 'createdAt']) &&
             request.resource.data.roadmapId is string &&
             request.resource.data.phaseId is string &&
             request.resource.data.tasks is list;
    }

    // Validate roadmap metadata structure
    function isValidRoadmapMetadata() {
      return request.resource.data.keys().hasAll(['userId', 'title', 'isPublic', 'createdAt']) &&
             request.resource.data.userId is string &&
             request.resource.data.userId == request.auth.uid &&
             request.resource.data.title is string &&
             request.resource.data.title.size() > 0 &&
             request.resource.data.isPublic is bool &&
             (!request.resource.data.keys().hasAny(['allowDownload']) || request.resource.data.allowDownload is bool);
    }

    // Check if user can only update allowed fields
    function isValidUpdate(allowedFields) {
      return request.resource.data.diff(resource.data).affectedKeys().hasOnly(allowedFields);
    }

    // ============================================================================
    // USER PROFILES
    // ============================================================================

    match /users/{userId} {
      // Users can read their own profile
      allow read: if isOwner(userId);

      // Users can create their own profile with valid data
      allow create: if isOwner(userId) && isValidUserData();

      // Users can update their own profile (restricted fields)
      allow update: if isOwner(userId) &&
                    isValidUpdate(['displayName', 'photoURL', 'preferences', 'lastLoginAt', 'updatedAt']);

      // Users cannot delete their profile (use Firebase Auth for account deletion)
      allow delete: if false;
    }

    // ============================================================================
    // ROADMAPS (Full Data)
    // ============================================================================

    match /roadmaps/{roadmapId} {
      // Read access: Own roadmaps (requires auth) OR public roadmaps (no auth required)
      allow read: if (isAuthenticated() && isOwner(resource.data.userId)) || isPublic();

      // Create: Authenticated users can create roadmaps with valid data
      allow create: if isAuthenticated() && isValidRoadmapData();

      // Update: Only owners can update their roadmaps (expanded fields for editor support)
      allow update: if isAuthenticated() &&
                    isOwner(resource.data.userId) &&
                    isValidUpdate(['outline', 'isPublic', 'allowDownload', 'updatedAt', 'lastAccessed', 'version', 'tags', 'title', 'description', 'project_level']);

      // Delete: Only owners can delete their roadmaps
      allow delete: if isAuthenticated() && isOwner(resource.data.userId);
    }

    // ============================================================================
    // PHASE TASKS (Split from Roadmaps for Size Optimization)
    // ============================================================================

    match /phaseTasks/{phaseTaskId} {
      // Read access: User owns the parent roadmap (requires auth) OR roadmap is public (no auth required)
      allow read: if (
        // Check if user owns the roadmap (requires authentication)
        (isAuthenticated() &&
         exists(/databases/$(database)/documents/roadmaps/$(resource.data.roadmapId)) &&
         get(/databases/$(database)/documents/roadmaps/$(resource.data.roadmapId)).data.userId == request.auth.uid) ||
        // OR roadmap is public (no authentication required)
        (exists(/databases/$(database)/documents/roadmaps/$(resource.data.roadmapId)) &&
         get(/databases/$(database)/documents/roadmaps/$(resource.data.roadmapId)).data.isPublic == true)
      );

      // Create: Allow authenticated users to create phase tasks (roadmap ownership validated by batch operation)
      allow create: if isAuthenticated() && isValidPhaseTasksData();

      // Update: Only if user owns the parent roadmap (expanded fields for editor support)
      allow update: if isAuthenticated() &&
                    exists(/databases/$(database)/documents/roadmaps/$(resource.data.roadmapId)) &&
                    get(/databases/$(database)/documents/roadmaps/$(resource.data.roadmapId)).data.userId == request.auth.uid &&
                    isValidUpdate(['tasks', 'updatedAt', 'createdAt', 'phaseId', 'phaseNumber', 'roadmapId']);

      // Delete: Only if user owns the parent roadmap
      allow delete: if isAuthenticated() &&
                    exists(/databases/$(database)/documents/roadmaps/$(resource.data.roadmapId)) &&
                    get(/databases/$(database)/documents/roadmaps/$(resource.data.roadmapId)).data.userId == request.auth.uid;
    }

    // ============================================================================
    // ROADMAP METADATA (Optimized for Queries)
    // ============================================================================

    match /roadmapMetadata/{roadmapId} {
      // Read access: Own metadata (requires auth) OR public metadata (no auth required)
      allow read: if (isAuthenticated() && isOwner(resource.data.userId)) || isPublic();

      // Create: Authenticated users can create metadata with valid data
      allow create: if isAuthenticated() && isValidRoadmapMetadata();

      // Update: Only owners can update their metadata (restricted fields)
      allow update: if isAuthenticated() &&
                    isOwner(resource.data.userId) &&
                    isValidUpdate(['title', 'description', 'isPublic', 'allowDownload', 'updatedAt', 'lastAccessed',
                                  'totalPhases', 'totalTasks', 'progressPercentage', 'tags', 'projectLevel']);

      // Delete: Only owners can delete their metadata
      allow delete: if isAuthenticated() && isOwner(resource.data.userId);
    }

    // ============================================================================
    // TASK COMPLETIONS
    // ============================================================================

    match /taskCompletions/{userId} {
      // Users can only access their own completion data
      allow read, write: if isOwner(userId);

      // Nested roadmap completions
      match /roadmaps/{roadmapId} {
        // Users can access their own completion data
        allow read, write: if isOwner(userId);

        // Allow reading completion stats for public roadmaps (anonymized)
        allow read: if isAuthenticated() &&
                    exists(/databases/$(database)/documents/roadmaps/$(roadmapId)) &&
                    get(/databases/$(database)/documents/roadmaps/$(roadmapId)).data.isPublic == true;
      }
    }

    // ============================================================================
    // USER PREFERENCES
    // ============================================================================

    match /userPreferences/{userId} {
      // Users can only access their own preferences
      allow read, write: if isOwner(userId);

      // Validate preferences structure on create/update
      allow create, update: if isOwner(userId) &&
                            request.resource.data.keys().hasAll(['theme']) &&
                            request.resource.data.theme in ['light', 'dark', 'auto'];
    }

    // ============================================================================
    // PUBLIC STATISTICS (Read-Only)
    // ============================================================================

    match /publicStats/{document=**} {
      // All authenticated users can read public statistics
      allow read: if isAuthenticated();

      // Only server-side functions can write statistics
      allow write: if false;
    }

    // ============================================================================
    // ADMIN COLLECTIONS (Future Use)
    // ============================================================================

    match /adminData/{document=**} {
      // Only admin users can access admin data
      // TODO: Implement admin role checking when needed
      allow read, write: if false;
    }

    // ============================================================================
    // SECURITY: Deny all other access
    // ============================================================================

    // Explicitly deny access to any other collections
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
