{"type": "object", "properties": {"roadmap_title": {"type": "string"}, "roadmap_description": {"type": "string"}, "num_of_phases": {"type": "integer"}, "project_level": {"type": "string", "enum": ["beginner", "intermediate", "advanced", "expert"]}, "phases": {"type": "array", "items": {"type": "object", "properties": {"phase_title": {"type": "string"}, "phase_summary": {"type": "string"}, "phase_id": {"type": "string"}, "num_of_tasks": {"type": "integer"}, "phase_details": {"type": "array", "items": {"type": "string"}}, "phase_number": {"type": "integer"}, "phase_dependencies": {"type": "array", "items": {"type": "string"}}, "key_milestones": {"type": "array", "items": {"type": "string"}}, "success_indicators": {"type": "array", "items": {"type": "string"}}}, "required": ["phase_title", "phase_summary", "phase_id", "num_of_tasks", "phase_details", "phase_number", "phase_dependencies", "key_milestones", "success_indicators"]}}, "tags": {"type": "array", "items": {"type": "string"}}}, "required": ["roadmap_title", "roadmap_description", "num_of_phases", "project_level", "phases", "tags"]}