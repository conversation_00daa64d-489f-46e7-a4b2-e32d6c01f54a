{"type": "object", "properties": {"tasks": {"type": "array", "items": {"type": "object", "properties": {"phase_id": {"type": "string"}, "task_id": {"type": "string"}, "task_difficulty": {"type": "string", "enum": ["very_easy", "easy", "normal", "difficult", "challenging"]}, "task_title": {"type": "string"}, "task_summary": {"type": "string"}, "task_detail": {"type": "object", "properties": {"content": {"type": "string"}, "format": {"type": "string", "enum": ["markdown", "html", "plaintext"]}}, "required": ["content", "format"]}, "code_blocks": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "language": {"type": "string"}, "explanation": {"type": "string"}, "complexity": {"type": "string", "enum": ["basic", "intermediate", "advanced"]}}, "required": ["code", "language", "explanation", "complexity"]}}, "difficulty_reason": {"type": "string"}, "prerequisites_needed": {"type": "array", "items": {"type": "string"}}, "task_dependencies": {"type": "array", "items": {"type": "object", "properties": {"dependency_type": {"type": "string", "enum": ["required", "recommended", "optional"]}, "task_id": {"type": "string"}, "phase_id": {"type": "string"}}, "required": ["dependency_type", "task_id", "phase_id"]}}, "est_time": {"type": "object", "properties": {"min_time": {"type": "object", "properties": {"amount": {"type": "number"}, "unit": {"type": "string", "enum": ["minutes", "hours", "days", "weeks"]}}, "required": ["amount", "unit"]}, "max_time": {"type": "object", "properties": {"amount": {"type": "number"}, "unit": {"type": "string", "enum": ["minutes", "hours", "days", "weeks"]}}, "required": ["amount", "unit"]}, "factors_affecting_time": {"type": "array", "items": {"type": "string"}}}, "required": ["min_time", "max_time", "factors_affecting_time"]}, "resource_links": {"type": "array", "items": {"type": "object", "properties": {"display_text": {"type": "string"}, "url": {"type": "string"}, "is_essential": {"type": "boolean"}, "type": {"type": "string", "enum": ["document", "tutorial", "video", "article", "tool", "reference", "example", "course"]}}, "required": ["display_text", "url", "is_essential", "type"]}}, "task_priority": {"type": "string", "enum": ["low", "mid", "high", "critical"]}, "task_tags": {"type": "array", "items": {"type": "string"}}, "task_number": {"type": "integer"}}, "required": ["phase_id", "task_id", "task_difficulty", "task_title", "task_summary", "task_detail", "code_blocks", "difficulty_reason", "prerequisites_needed", "task_dependencies", "est_time", "resource_links", "task_priority", "task_tags", "task_number"]}}}, "required": ["tasks"]}